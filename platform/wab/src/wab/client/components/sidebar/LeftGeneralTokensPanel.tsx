// This is a skeleton starter React component generated by Plasmic.
// This file is owned by you, feel free to edit as you see fit.
import {
  RenderElementProps,
  VirtualTree,
  getFolderKeyChanges,
  useTreeData,
} from "@/wab/client/components/grouping/VirtualTree";
import { promptDeleteFolder } from "@/wab/client/components/modals/folderDeletionModal";
import MultiAssetsActions from "@/wab/client/components/sidebar/MultiAssetsActions";
import { TokenEditModal } from "@/wab/client/components/sidebar/TokenEditModal";
import TokenTypeHeader from "@/wab/client/components/sidebar/TokenTypeHeader";
import {
  TOKEN_ROW_HEIGHT,
  TokenControlsContext,
  TokenFolder,
  TokenFolderActions,
  TokenFolderRow,
  TokenPanelRow,
  TokenRow,
} from "@/wab/client/components/sidebar/token-controls";
import { Matcher } from "@/wab/client/components/view-common";
import { useClientTokenResolver } from "@/wab/client/components/widgets/ColorPicker/client-token-resolver";
import Select from "@/wab/client/components/widgets/Select";
import { PlasmicLeftGeneralTokensPanel } from "@/wab/client/plasmic/plasmic_kit_left_pane/PlasmicLeftGeneralTokensPanel";
import { useStudioCtx } from "@/wab/client/studio-ctx/StudioCtx";
import {
  TokenType,
  TokenValue,
  tokenTypeDefaults,
  tokenTypeLabel,
  tokenTypes,
} from "@/wab/commons/StyleToken";
import { VariantedStylesHelper } from "@/wab/shared/VariantedStylesHelper";
import { isScreenVariant } from "@/wab/shared/Variants";
import { ensure, unexpected, unreachable } from "@/wab/shared/common";
import { isHostLessPackage } from "@/wab/shared/core/sites";
import {
  Folder as InternalFolder,
  createFolderTreeStructure,
  getFolderTrimmed,
  getFolderWithSlash,
  isFolder,
  replaceFolderName,
} from "@/wab/shared/folders/folders-util";
import { ProjectDependency, StyleToken } from "@/wab/shared/model/classes";
import { naturalSort } from "@/wab/shared/sort";
import Chroma from "@/wab/shared/utils/color-utils";
import { debounce, groupBy, partition } from "lodash";
import { observer } from "mobx-react";
import * as React from "react";

interface TokenToPanelRowProps {
  item: StyleToken | InternalFolder<StyleToken>;
  tokenType: TokenType;
  getTokenValue: (token: StyleToken) => TokenValue;
  actions: TokenFolderActions;
  dep?: ProjectDependency;
}

function mapToTokenPanelRow({
  item,
  tokenType,
  getTokenValue,
  actions,
  dep,
}: TokenToPanelRowProps): TokenPanelRow {
  if (!isFolder(item)) {
    return {
      type: "token" as const,
      key: item.uuid,
      token: item,
      value: getTokenValue(item),
      importedFrom: dep?.projectId,
    };
  }

  return {
    type: "folder-token" as const,
    tokenType,
    key: item.path,
    name: item.name,
    path: item.path,
    items: item.items.map((i) =>
      mapToTokenPanelRow({ item: i, tokenType, getTokenValue, dep, actions })
    ),
    count: item.count,
    actions,
  };
}

const LeftGeneralTokensPanel = observer(function LeftGeneralTokensPanel() {
  const studioCtx = useStudioCtx();
  const [debouncedQuery, setDebouncedQuery] = React.useState("");
  const debouncedSetQuery = React.useCallback(
    debounce((value: string) => {
      setDebouncedQuery(value);
    }, 500),
    [setDebouncedQuery]
  );
  const [expandedHeaders, setExpandedHeaders] = React.useState<Set<TokenType>>(
    new Set()
  );
  const matcher = new Matcher(debouncedQuery);

  const [justAdded, setJustAdded] = React.useState<StyleToken | undefined>(
    undefined
  );

  const [editToken, setEditToken] = React.useState<StyleToken | undefined>(
    undefined
  );

  const [vsh, setVsh] = React.useState<VariantedStylesHelper | undefined>(
    undefined
  );

  const [isTargeting, setIsTargeting] = React.useState(false);
  const resolver = useClientTokenResolver();

  const getTokenValue = React.useCallback(
    (token: StyleToken) => {
      let value = resolver(token, vsh);
      if (token.type === TokenType.Color) {
        value = Chroma.stringify(value) as TokenValue;
      }
      return value;
    },
    [resolver, vsh]
  );

  const getRowKey = React.useCallback((row: TokenPanelRow) => {
    return row.key;
  }, []);
  const getRowChildren = React.useCallback((row: TokenPanelRow) => {
    if (row.type === "token") {
      return [];
    }
    return row.items;
  }, []);
  const getRowSearchText = React.useCallback((row: TokenPanelRow) => {
    switch (row.type) {
      case "header":
        return tokenTypeLabel(row.tokenType);
      case "folder":
      case "folder-token":
        return row.name;
      case "token":
        return `${row.token.name}$${row.value}`;
      default:
        unexpected();
    }
  }, []);
  const getRowHeight = React.useCallback((row: TokenPanelRow) => {
    if (row.type === "header") {
      return 42;
    }
    return TOKEN_ROW_HEIGHT;
  }, []);

  const onAddToken = React.useCallback(
    async (type: TokenType, folderName?: string) => {
      const folderPath = getFolderWithSlash(folderName);

      await studioCtx.change(({ success }) => {
        const initialValue = tokenTypeDefaults(type);
        const token = studioCtx.tplMgr().addToken({
          prefix: folderPath,
          tokenType: type,
          value: initialValue,
        });
        setJustAdded(token);
        setEditToken(token);
        return success();
      });
    },
    [studioCtx, setJustAdded, setEditToken]
  );

  const getFolderTokens = (
    items: TokenPanelRow[]
  ): { tokens: StyleToken[]; folders: TokenFolder[] } => {
    const tokens: StyleToken[] = [];
    const folders: TokenFolder[] = [];

    for (const item of items) {
      switch (item.type) {
        case "folder":
        case "folder-token": {
          folders.push(item);
          const children = getFolderTokens(item.items);
          tokens.push(...children.tokens);
          folders.push(...children.folders);
          break;
        }
        case "token":
          tokens.push(item.token);
          break;
        case "header":
          break;
      }
    }
    return { tokens, folders };
  };

  const onDeleteFolder = React.useCallback(
    async (folder: TokenFolder) => {
      const confirmation = await promptDeleteFolder(
        "token",
        getFolderWithSlash(folder.name),
        folder.count
      );
      if (confirmation) {
        const { tokens } = getFolderTokens([folder]);
        await studioCtx.siteOps().tryDeleteTokens(tokens);
      }
    },
    [studioCtx]
  );

  const onFolderRenamed = React.useCallback(
    async (folder: TokenFolder, newName: string) => {
      const pathData = replaceFolderName(folder.key, newName);
      const { tokens, folders } = getFolderTokens([folder]);

      await studioCtx.changeUnsafe(() => {
        const { oldPath, newPath } = pathData;
        for (const token of tokens) {
          const oldTokenName = token.name;
          const newTokenName = oldTokenName.replace(oldPath, newPath);
          studioCtx.tplMgr().renameToken(token, newTokenName);
        }
      });
      const keyChanges = getFolderKeyChanges(folders, pathData);
      renameGroup(keyChanges);
    },
    [studioCtx]
  );

  const actions: TokenFolderActions = React.useMemo(
    () => ({
      onAddToken,
      onDeleteFolder,
      onFolderRenamed,
    }),
    [onAddToken, onDeleteFolder, onFolderRenamed]
  );

  const onDuplicate = React.useCallback(
    async (token: StyleToken) => {
      await studioCtx.change(({ success }) => {
        const newToken = studioCtx.tplMgr().duplicateToken(token);
        setJustAdded(newToken);
        setEditToken(newToken);
        return success();
      });
    },
    [studioCtx, setJustAdded, setEditToken]
  );

  const onSelect = React.useCallback(
    (token: StyleToken) => {
      setEditToken(token);
    },
    [setEditToken]
  );

  const nonScreenGlobalVariants = studioCtx.site.globalVariantGroups.flatMap(
    (variantGroup) => variantGroup.variants.filter((v) => !isScreenVariant(v))
  );

  const handleGlobalVariantChange = (variantId) => {
    if (variantId === "base") {
      setVsh(undefined);
      setIsTargeting(false);
    } else {
      const globalVariants = [
        nonScreenGlobalVariants.some((v) => v.uuid === variantId)
          ? ensure(
              nonScreenGlobalVariants.find((v) => v.uuid === variantId),
              () => `Picked unknown screen variant`
            )
          : ensure(
              studioCtx.site.activeScreenVariantGroup?.variants.find(
                (v) => v.uuid === variantId
              ),
              () => `Picked unknown global variant`
            ),
      ];
      setVsh(
        new VariantedStylesHelper(
          studioCtx.site,
          globalVariants,
          globalVariants
        )
      );
      setIsTargeting(true);
    }
  };

  const tokensByType = groupBy(studioCtx.site.styleTokens, (t) => t.type);

  const tokenSectionItems = (tokenType: TokenType) => {
    const makeTokensItems = (tokens: StyleToken[], dep?: ProjectDependency) => {
      tokens = naturalSort(tokens, (token) => getFolderTrimmed(token.name));
      const tokenTree = createFolderTreeStructure(tokens, {
        pathPrefix: tokenType,
        getName: (item) => item.name,
        mapper: (item) =>
          mapToTokenPanelRow({
            item,
            tokenType,
            getTokenValue,
            dep,
            actions,
          }),
      });
      return { items: tokenTree, count: tokens.length };
    };

    const makeDepsItems = (deps: ProjectDependency[]): TokenPanelRow[] => {
      deps = naturalSort(deps, (dep) =>
        studioCtx.projectDependencyManager.getNiceDepName(dep)
      );
      return deps
        .map((dep) => {
          return {
            type: "folder" as const,
            tokenType,
            name: studioCtx.projectDependencyManager.getNiceDepName(dep),
            key: `${tokenType}-${dep.uuid}`,
            // We only include registered tokens if they're from a hostless
            // package; otherwise, registered tokens from custom host will
            // already show up in the RegisteredTokens section.
            ...makeTokensItems(
              (isHostLessPackage(dep.site)
                ? dep.site.styleTokens
                : dep.site.styleTokens.filter((t) => !t.isRegistered)
              ).filter((t) => t.type === tokenType),
              dep
            ),
            actions,
          };
        })
        .filter((dep) => dep.count > 0);
    };

    const tokens = tokensByType[tokenType] ?? [];

    const [normalTokens, registeredTokens] = partition(
      tokens,
      (t) => !t.isRegistered
    );

    const items: TokenPanelRow[] = [
      ...makeTokensItems(normalTokens).items,
      ...(registeredTokens.length > 0
        ? [
            {
              type: "folder" as const,
              tokenType,
              name: "Registered tokens",
              key: `$${tokenType}-registered-folder`,
              actions,
              ...makeTokensItems(registeredTokens),
            },
          ]
        : []),
      ...makeDepsItems(
        studioCtx.site.projectDependencies.filter(
          (d) => !isHostLessPackage(d.site)
        )
      ),
      ...makeDepsItems(
        studioCtx.site.projectDependencies.filter((d) =>
          isHostLessPackage(d.site)
        )
      ),
    ];
    const totalCount = items.reduce(
      (acc, item) => (item.type !== "token" ? acc + item.count : acc + 1),
      0
    );
    return { items, count: totalCount };
  };

  const tokensContent = () => {
    const selectableTokens = studioCtx.site.styleTokens
      .filter((t) => {
        let resolved = resolver(t, vsh);
        if (t.type === TokenType.Color) {
          resolved = Chroma.stringify(resolved) as TokenValue;
        }
        return (
          (matcher.matches(t.name) ||
            matcher.matches(resolved) ||
            justAdded === t) &&
          !t.isRegistered
        );
      })
      .map((t) => t.uuid);

    const items = tokenTypes.map((tokenType): TokenPanelRow => {
      return {
        type: "header" as const,
        tokenType: tokenType,
        key: `$${tokenType}-folder`,
        ...tokenSectionItems(tokenType),
      };
    });

    return (
      <MultiAssetsActions
        type="token"
        selectableAssets={selectableTokens}
        onDelete={async (selected: string[]) => {
          const selectedTokens = studioCtx.site.styleTokens.filter((t) =>
            selected.includes(t.uuid)
          );
          return await studioCtx.siteOps().tryDeleteTokens(selectedTokens);
        }}
      >
        <TokenControlsContext.Provider
          value={{
            vsh,
            resolver,
            onDuplicate,
            onSelect,
            onAdd: onAddToken,
            expandedHeaders,
            setExpandedHeaders,
          }}
        >
          <VirtualTree
            rootNodes={items}
            renderElement={TokenTreeRow}
            nodeData={nodeData}
            nodeKey={nodeKey}
            nodeHeights={nodeHeights}
            expandAll={expandAll}
            collapseAll={collapseAll}
          />
        </TokenControlsContext.Provider>
      </MultiAssetsActions>
    );
  };

  const treeItems = React.useMemo(
    (): TokenPanelRow[] =>
      tokenTypes.map((tt) => {
        const { items: section, count } = tokenSectionItems(tt);
        return {
          type: "header",
          tokenType: tt,
          key: `$${tt}-hdr`,
          items: section,
          count,
        };
      }),
    [tokenSectionItems]
  );

  const {
    nodeData,
    nodeKey,
    nodeHeights,
    renameGroup,
    expandAll,
    collapseAll,
  } = useTreeData<TokenPanelRow>({
    nodes: treeItems,
    query: debouncedQuery,
    renderElement: TokenTreeRow,
    getNodeKey: getRowKey,
    getNodeChildren: getRowChildren,
    getNodeSearchText: getRowSearchText,
    getNodeHeight: getRowHeight,
    defaultOpenKeys: "all",
  });

  return (
    <>
      <PlasmicLeftGeneralTokensPanel
        leftPaneHeader={{
          actions: null,
        }}
        leftSearchPanel={{
          searchboxProps: {
            onChange: (e) => {
              debouncedSetQuery(e.target.value);
            },
            autoFocus: true,
          },
          expandProps: {
            onClick: expandAll,
          },
          collapseProps: {
            onClick: collapseAll,
          },
        }}
        isTargeting={isTargeting}
        globalVariantSelect={{
          onChange: (e) => handleGlobalVariantChange(e),
          children: (
            <>
              <Select.Option value="base">Base</Select.Option>
              {studioCtx.site.activeScreenVariantGroup?.variants &&
                studioCtx.site.activeScreenVariantGroup.variants.length > 0 && (
                  <Select.OptionGroup title="Screen Variants">
                    {studioCtx.site.activeScreenVariantGroup.variants.map(
                      (variant) => (
                        <Select.Option value={variant.uuid} key={variant.uuid}>
                          {variant.name}
                        </Select.Option>
                      )
                    )}
                  </Select.OptionGroup>
                )}
              {nonScreenGlobalVariants.length > 0 && (
                <Select.OptionGroup title="Global Variants">
                  {nonScreenGlobalVariants.map((variant) => (
                    <Select.Option value={variant.uuid} key={variant.uuid}>
                      {variant.name}
                    </Select.Option>
                  ))}
                </Select.OptionGroup>
              )}
            </>
          ),
        }}
        content={<>{tokensContent()}</>}
      />

      {editToken && (
        <TokenEditModal
          studioCtx={studioCtx}
          token={editToken}
          onClose={() => {
            setEditToken(undefined);
            setJustAdded(undefined);
          }}
          autoFocusName={justAdded === editToken}
          vsh={vsh}
        />
      )}
    </>
  );
});

const TokenTreeRow = (props: RenderElementProps<TokenPanelRow>) => {
  const { value, treeState } = props;
  switch (value.type) {
    case "header":
      return (
        <TokenTypeHeader
          tokenType={value.tokenType}
          isExpanded={treeState.isOpen}
          toggleExpand={treeState.toggleExpand}
          groupSize={value.count}
        />
      );
    case "folder":
    case "folder-token":
      return (
        <TokenFolderRow
          folder={value}
          matcher={treeState.matcher}
          isOpen={treeState.isOpen}
          indentMultiplier={treeState.level - 1}
          toggleExpand={treeState.toggleExpand}
        />
      );
    case "token":
      return (
        <TokenRow
          token={value.token}
          tokenValue={value.value}
          matcher={treeState.matcher}
          isImported={!!value.importedFrom}
          indentMultiplier={treeState.level - 1}
        />
      );
    default:
      unreachable(value);
  }
};

export default LeftGeneralTokensPanel;
