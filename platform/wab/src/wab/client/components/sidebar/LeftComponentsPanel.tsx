// This is a skeleton starter React component generated by Plasmic.
// This file is owned by you, feel free to edit as you see fit.
import {
  RenderElementProps,
  VirtualTree,
  getFolderKeyChanges,
  useTreeData,
} from "@/wab/client/components/grouping/VirtualTree";
import { promptDeleteFolder } from "@/wab/client/components/modals/folderDeletionModal";
import {
  ComponentFolder,
  ComponentFolderActions,
  ComponentFolderRow,
  ComponentPanelRow,
} from "@/wab/client/components/sidebar/ComponentFolderRow";
import { ComponentRow } from "@/wab/client/components/sidebar/ComponentRow";
import { PlasmicLeftComponentsPanel } from "@/wab/client/plasmic/plasmic_kit_left_pane/PlasmicLeftComponentsPanel";
import { useStudioCtx } from "@/wab/client/studio-ctx/StudioCtx";
import { isBuiltinCodeComponent } from "@/wab/shared/code-components/builtin-code-components";
import { isNonNil, unreachable } from "@/wab/shared/common";
import {
  getComponentDisplayName,
  getFolderComponentTrimmedName,
  isCodeComponent,
  isContextCodeComponent,
  isHostLessCodeComponent,
  isPageComponent,
  isReusableComponent,
  isShownHostLessCodeComponent,
  sortComponentsByName,
} from "@/wab/shared/core/components";
import { isHostLessPackage } from "@/wab/shared/core/sites";
import { isAdminTeamEmail } from "@/wab/shared/devflag-utils";
import {
  Folder as InternalFolder,
  createFolderTreeStructure,
  getFolderWithSlash,
  isFolder,
  replaceFolderName,
} from "@/wab/shared/folders/folders-util";
import { Component, ProjectDependency } from "@/wab/shared/model/classes";
import { naturalSort } from "@/wab/shared/sort";
import { debounce } from "lodash";
import { observer } from "mobx-react";
import * as React from "react";

interface ComponentToPanelRowProps {
  item: Component | InternalFolder<Component>;
  dep?: ProjectDependency;
  actions: ComponentFolderActions;
}

function mapToComponentPanelRow({
  item,
  dep,
  actions,
}: ComponentToPanelRowProps): ComponentPanelRow {
  if (!isFolder(item)) {
    return {
      type: "component" as const,
      key: item.uuid,
      component: item,
      importedFrom: dep?.projectId,
    };
  }

  return {
    type: "folder-component" as const,
    key: item.path,
    name: item.name,
    path: item.path,
    items: item.items.map((i) =>
      mapToComponentPanelRow({ item: i, dep, actions })
    ),
    count: item.count,
    actions,
  };
}

const getFolderComponents = (
  items: ComponentPanelRow[]
): { components: Component[]; folders: ComponentFolder[] } => {
  const components: Component[] = [];
  const folders: ComponentFolder[] = [];

  for (const item of items) {
    switch (item.type) {
      case "folder":
      case "folder-component": {
        folders.push(item);
        const children = getFolderComponents(item.items);
        components.push(...children.components);
        folders.push(...children.folders);
        break;
      }
      case "component":
        components.push(item.component);
        break;
    }
  }
  return { components, folders };
};

const LeftComponentsPanel = observer(function LeftComponentsPanel() {
  const studioCtx = useStudioCtx();
  const [debouncedQuery, setDebouncedQuery] = React.useState("");
  const debouncedSetQuery = React.useCallback(
    debounce((value: string) => {
      setDebouncedQuery(value);
    }, 500),
    [setDebouncedQuery]
  );
  const getRowKey = React.useCallback((row: ComponentPanelRow) => {
    return row.key;
  }, []);
  const getRowChildren = React.useCallback((row: ComponentPanelRow) => {
    if (row.type === "component") {
      return [];
    }
    return row.items;
  }, []);
  const getRowSearchText = React.useCallback((row: ComponentPanelRow) => {
    if (row.type === "component") {
      return getComponentDisplayName(row.component);
    }
    return row.name;
  }, []);
  const getRowHeight = React.useCallback(() => {
    return 32;
  }, []);

  const readOnly = studioCtx.getLeftTabPermission("components") === "readable";

  const isAdmin = isAdminTeamEmail(
    studioCtx.appCtx.selfInfo?.email,
    studioCtx.appCtx.appConfig
  );

  const onDeleteFolder = React.useCallback(
    async (folder: ComponentFolder) => {
      const confirmation = await promptDeleteFolder(
        "component",
        getFolderWithSlash(folder.name),
        folder.count
      );
      if (confirmation) {
        await studioCtx.changeUnsafe(() => {
          const { components } = getFolderComponents([folder]);
          for (const component of components) {
            studioCtx.siteOps().tryRemoveComponent(component);
          }
        });
      }
    },
    [studioCtx]
  );

  const onFolderRenamed = React.useCallback(
    async (folder: ComponentFolder, newName: string) => {
      const pathData = replaceFolderName(folder.key, newName);
      const { components, folders } = getFolderComponents([folder]);

      await studioCtx.changeUnsafe(() => {
        const { oldPath, newPath } = pathData;
        for (const component of components) {
          const newComponentName = component.name.replace(oldPath, newPath);
          studioCtx.siteOps().tryRenameComponent(component, newComponentName);
        }
      });
      const keyChanges = getFolderKeyChanges(folders, pathData);
      renameGroup(keyChanges);
    },
    [studioCtx]
  );

  const onAddComponent = React.useCallback(
    async (folderName?: string) => {
      const folderPath = getFolderWithSlash(folderName);
      await studioCtx.siteOps().createFrameForNewComponent(folderPath);
    },
    [studioCtx]
  );

  const actions: ComponentFolderActions = React.useMemo(
    () => ({
      onAddComponent,
      onDeleteFolder,
      onFolderRenamed,
    }),
    [onAddComponent, onDeleteFolder, onFolderRenamed]
  );

  const makeCompsItems = (
    comps: Component[],
    pathPrefix: string,
    dep?: ProjectDependency
  ): { items: ComponentPanelRow[]; count: number } => {
    comps = comps.filter(
      (comp) =>
        isReusableComponent(comp) &&
        !isBuiltinCodeComponent(comp) &&
        !isContextCodeComponent(comp) &&
        (!isHostLessCodeComponent(comp) ||
          isShownHostLessCodeComponent(
            comp,
            studioCtx.appCtx.appConfig.hostLessComponents
          ))
    );
    comps = sortComponentsByName(comps);
    const componentTree = createFolderTreeStructure(comps, {
      pathPrefix,
      getName: (item) => getFolderComponentTrimmedName(item),
      mapper: (item) => mapToComponentPanelRow({ item, dep, actions }),
    });

    return { items: componentTree, count: comps.length };
  };

  const makeDepsItems = (deps: ProjectDependency[]): ComponentFolder[] => {
    deps = naturalSort(deps, (dep) =>
      studioCtx.projectDependencyManager.getNiceDepName(dep)
    );

    return deps
      .map((dep) => {
        const { items, count } = makeCompsItems(
          dep.site.components.filter(
            (c) =>
              isReusableComponent(c) &&
              (isHostLessPackage(dep.site) || !isCodeComponent(c))
          ),
          dep.uuid,
          dep
        );
        return {
          type: "folder" as const,
          name: `${studioCtx.projectDependencyManager.getNiceDepName(dep)}`,
          key: dep.uuid,
          // For deps, we only show code components from hostless packages; for non-hostless
          // packages, ony the code components from the current host page count, and they're
          // shown in the Code components section
          items: items,
          count: count,
          actions,
        };
      })
      .filter((folder) => folder.count > 0);
  };

  const plainComponents = studioCtx.site.components.filter(
    (c) => isReusableComponent(c) && !isCodeComponent(c)
  );
  const codeComponents = studioCtx.site.components.filter(
    (c) =>
      isReusableComponent(c) &&
      isCodeComponent(c) &&
      !isHostLessCodeComponent(c) &&
      !isContextCodeComponent(c)
  );

  // Show non-hostless packages first, then hostless packages
  const importedComponentItems = [
    ...makeDepsItems(
      studioCtx.site.projectDependencies.filter(
        (d) => !isHostLessPackage(d.site)
      )
    ),
    ...makeDepsItems(
      studioCtx.site.projectDependencies.filter((d) =>
        isHostLessPackage(d.site)
      )
    ),
  ].filter((folder) => folder.count > 0);

  const items: ComponentPanelRow[] = [
    ...makeCompsItems(plainComponents, "").items,
    ...(codeComponents.length > 0
      ? [
          {
            type: "folder" as const,
            name: "Code components",
            key: `$code-components-folder`,
            actions,
            ...makeCompsItems(codeComponents, "$code-components-folder"),
          },
        ]
      : []),
    ...(importedComponentItems.length > 0
      ? [
          {
            type: "folder" as const,
            name: "Imported",
            key: "$imports-folder",
            items: importedComponentItems,
            count: importedComponentItems.reduce(
              (sum, folder) => sum + folder.count,
              0
            ),
            actions,
          },
        ]
      : []),
    ...(isAdmin
      ? studioCtx.site.projectDependencies
          .filter((d) => !isHostLessPackage(d.site))
          .map((dep) => {
            const pageComponents = sortComponentsByName(dep.site.components)
              .filter((c) => isPageComponent(c))
              .map((comp) => ({
                type: "component" as const,
                component: comp,
                key: comp.uuid,
                importedFrom: dep.projectId,
              }));
            if (pageComponents.length === 0) {
              return undefined;
            }
            return {
              type: "folder" as const,
              name: `PAGES FROM ${dep.name} (DO NOT USE)`,
              key: `$pages-${dep.uuid}`,
              // For deps, we only show code components from hostless packages; for non-hostless
              // packages, ony the code components from the current host page count, and they're
              // shown in the Code components section
              items: pageComponents,
              count: pageComponents.length,
              actions,
            };
          })
          .filter(isNonNil)
      : []),
  ];

  const {
    nodeData,
    nodeKey,
    nodeHeights,
    renameGroup,
    expandAll,
    collapseAll,
  } = useTreeData<ComponentPanelRow>({
    nodes: items,
    query: debouncedQuery,
    renderElement: ComponentTreeRow,
    getNodeKey: getRowKey,
    getNodeChildren: getRowChildren,
    getNodeSearchText: getRowSearchText,
    getNodeHeight: getRowHeight,
    defaultOpenKeys: "all",
  });

  return (
    <PlasmicLeftComponentsPanel
      root={{
        props: {
          "data-test-id": "components-tab",
        } as any,
      }}
      leftSearchPanel={{
        searchboxProps: {
          onChange: (e) => {
            debouncedSetQuery(e.target.value);
          },
          autoFocus: true,
        },
      }}
      newComponentButton={
        readOnly
          ? { render: () => null }
          : {
              onClick: () => studioCtx.siteOps().createFrameForNewComponent(),
            }
      }
      content={
        <>
          <VirtualTree
            rootNodes={items}
            renderElement={ComponentTreeRow}
            nodeData={nodeData}
            nodeKey={nodeKey}
            nodeHeights={nodeHeights}
            expandAll={expandAll}
            collapseAll={collapseAll}
          />
        </>
      }
    />
  );
});

const ComponentTreeRow = (props: RenderElementProps<ComponentPanelRow>) => {
  const { value, treeState } = props;
  switch (value.type) {
    case "folder":
    case "folder-component":
      return (
        <ComponentFolderRow
          folder={value}
          matcher={treeState.matcher}
          isOpen={treeState.isOpen}
          indentMultiplier={treeState.level}
          toggleExpand={treeState.toggleExpand}
        />
      );
    case "component":
      return (
        <ComponentRow
          component={value.component}
          matcher={treeState.matcher}
          importedFrom={value.importedFrom}
          indentMultiplier={treeState.level}
        />
      );
    default:
      unreachable(value);
  }
};

export default LeftComponentsPanel;
