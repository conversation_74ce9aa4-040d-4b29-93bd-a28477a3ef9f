// This is a skeleton starter React component generated by Plasmic.
// This file is owned by you, feel free to edit as you see fit.
import {
  isTokenReadOnly,
  useTokenControls,
} from "@/wab/client/components/sidebar/token-controls";
import PlasmicTokenTypeHeader, {
  DefaultTokenTypeHeaderProps,
} from "@/wab/client/plasmic/plasmic_kit_left_pane/PlasmicTokenTypeHeader";
import { useStudioCtx } from "@/wab/client/studio-ctx/StudioCtx";
import { TokenType, tokenTypeLabel } from "@/wab/commons/StyleToken";
import { MultiChoiceArg } from "@plasmicapp/react-web";
import * as React from "react";

interface TokenTypeHeaderProps extends DefaultTokenTypeHeaderProps {
  tokenType: TokenType;
  isExpanded?: boolean;
  toggleExpand: () => void;
}

const PREVIOUS_TOKEN_TYPES: Record<TokenType, TokenType> = {
  Color: TokenType.Color,
  FontFamily: TokenType.Color,
  FontSize: TokenType.FontFamily,
  LineHeight: TokenType.FontSize,
  Opacity: TokenType.LineHeight,
  Spacing: TokenType.Opacity,
};

function TokenTypeHeader(props: TokenTypeHeaderProps) {
  const { isExpanded, tokenType, toggleExpand, ...rest } = props;
  const studioCtx = useStudioCtx();
  const tokenControls = useTokenControls();

  React.useEffect(() => {
    tokenControls.setExpandedHeaders((set) => {
      if (isExpanded && !set.has(tokenType)) {
        set.add(tokenType);
      } else if (!isExpanded && set.has(tokenType)) {
        set.delete(tokenType);
      } else {
        return set;
      }

      return new Set(set);
    });
  }, [isExpanded]);

  const readOnly = isTokenReadOnly(studioCtx);

  const borders: MultiChoiceArg<"bottom" | "top"> = [
    "bottom" as const,
    ...(tokenType !== TokenType.Color &&
    tokenControls.expandedHeaders.has(PREVIOUS_TOKEN_TYPES[tokenType])
      ? ["top" as const]
      : []),
  ];

  return (
    <PlasmicTokenTypeHeader
      tokenType={tokenTypeLabel(tokenType)}
      addButton={
        readOnly
          ? { render: () => null }
          : {
              props: {
                onClick: async (e) => {
                  e.stopPropagation();
                  await tokenControls.onAdd(tokenType);
                  if (!isExpanded) {
                    toggleExpand();
                  }
                },
              },
            }
      }
      isExpanded={isExpanded}
      border={borders}
      {...rest}
    />
  );
}

export default TokenTypeHeader;
