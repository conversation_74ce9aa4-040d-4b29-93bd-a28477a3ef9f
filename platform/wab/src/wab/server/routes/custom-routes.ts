import {
  queryCopilot,
  queryCopilotFeedback,
  queryUiCopilot,
  saveCopilotFeedback,
} from "@/wab/server/routes/internal/copilot";
import {
  addGuidewireRoutes,
  createGuidewireTeam,
} from "@/wab/server/routes/internal/guidewire";
import {
  checkDomain,
  getDomainsForProject,
  getHostingSettings,
  getProjectIdAndTokenForDomain,
  getRecordPlasmicHostingHit,
  recordPlasmicHostingHit,
  revalidatePlasmicHosting,
  setCustomDomainForProject,
  setShowHostingBadge,
  setSubdomainForProject,
  updateHostingSettings,
} from "@/wab/server/routes/internal/hosting";
import * as paymentRoutes from "@/wab/server/routes/internal/payment";
import { startFreeTrial } from "@/wab/server/routes/internal/team-plans";
import { adminOnly, withNext } from "@/wab/server/routes/util";
import { Application, Request, Response } from "express";

export const ROUTES_WITH_TIMING = ["project-token-for-domain", "hosting-hit"];

function addPublicHostingRoutes(app: Application) {
  app.get(
    "/api/v1/project-token-for-domain",
    withNext(getProjectIdAndTokenForDomain)
  );
  app.get("/api/v1/hosting-hit", withNext(getRecordPlasmicHostingHit));
  app.post("/api/v1/hosting-hit", withNext(recordPlasmicHostingHit));
}

function addHostingRoutes(app: Application) {
  addPublicHostingRoutes(app);

  app.get(
    "/api/v1/domains-for-project/:projectId",
    withNext(getDomainsForProject)
  );
  app.get("/api/v1/check-domain", withNext(checkDomain));
  app.put(
    "/api/v1/custom-domain-for-project",
    withNext(setCustomDomainForProject)
  );
  app.put("/api/v1/subdomain-for-project", withNext(setSubdomainForProject));

  app.post("/api/v1/revalidate-hosting", withNext(revalidatePlasmicHosting));
  app.put(
    "/api/v1/projects/:projectId/hosting/badge",
    withNext(setShowHostingBadge)
  );
  app.put(
    "/api/v1/projects/:projectId/hosting/robots",
    withNext(updateHostingSettings)
  );

  app.get("/api/v1/plasmic-hosting/:projectId", withNext(getHostingSettings));
  app.put(
    "/api/v1/plasmic-hosting/:projectId",
    withNext(updateHostingSettings)
  );
}

function addCopilotRoutes(app: Application) {
  /**
   * Copilot routes
   */
  app.post("/api/v1/copilot", withNext(queryCopilot));
  app.post("/api/v1/copilot/ui", withNext(queryUiCopilot));
  app.post("/api/v1/copilot-feedback", withNext(saveCopilotFeedback));
  app.get(
    "/api/v1/copilot-feedback",
    adminOnly,
    withNext(queryCopilotFeedback)
  );
}

export function addInternalRoutes(app: Application) {
  addHostingRoutes(app);
  addPaymentRoutes(app);
  addCopilotRoutes(app);
  addGuidewireRoutes(app);
}

export function addInternalIntegrationsRoutes(app: Application) {
  addPublicHostingRoutes(app);
}

function addPaymentRoutes(app: Application) {
  /**
   * Billing
   */
  app.post("/api/v1/teams/:teamId/trial", withNext(startFreeTrial));
  app.post(
    "/api/v1/teams/:teamId/paymentMethod",
    withNext(paymentRoutes.updatePaymentMethod)
  );
  app.post(
    "/api/v1/billing/subscription/create",
    withNext(paymentRoutes.createSubscription)
  );
  app.get(
    "/api/v1/billing/subscription/:teamId",
    withNext(paymentRoutes.getSubscription)
  );
  app.post(
    "/api/v1/billing/subscription/create",
    withNext(paymentRoutes.createSubscription)
  );
  app.put(
    "/api/v1/billing/subscription/:teamId",
    withNext(paymentRoutes.changeSubscription)
  );
  app.delete(
    "/api/v1/billing/subscription/:teamId",
    withNext(paymentRoutes.cancelSubscription)
  );
  app.post(
    "/api/v1/billing/setup-intent/:teamId",
    withNext(paymentRoutes.createSetupIntent)
  );
}

export async function customCreateTeam(req: Request, res: Response) {
  if (req.headers["x-gw-api-access-token"]) {
    await createGuidewireTeam(req, res);
    return true;
  }
  return false;
}

export function isCustomPublicApiRequest(req: Request) {
  // Guidewire
  if (req.headers?.["x-gw-api-access-token"]) {
    return true;
  }
  return false;
}
