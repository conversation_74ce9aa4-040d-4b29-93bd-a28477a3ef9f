import { interact } from "@/wab/server/copilot/internal/llm-interactions";
import { createCopilotUiParseRequestParams } from "@/wab/server/copilot/internal/prompts";
import {
  createAnthropicClient,
  createOpenAIClient,
  getOpenAI,
} from "@/wab/server/copilot/llms";
import { DbMgr } from "@/wab/server/db/DbMgr";
import { superDbMgr, userDbMgr } from "@/wab/server/routes/util";
import { BadRequestError } from "@/wab/shared/ApiErrors/errors";
import {
  CopilotResponseData,
  QueryCopilotChatRequest,
  QueryCopilotCodeRequest,
  QueryCopilotFeedbackResponse,
  QueryCopilotRequest,
  QueryCopilotResponse,
  QueryCopilotSqlCodeRequest,
  QueryCopilotUiRequest,
  QueryCopilotUiResponse,
  SendCopilotFeedbackRequest,
} from "@/wab/shared/ApiSchema";
import { ensure, ensureType, maybe } from "@/wab/shared/common";
import {
  createAndRunCopilotCodeChain,
  createAndRunCopilotSqlCodeChain,
} from "@/wab/shared/copilot/internal/prompts";
import {
  CopilotUiActions,
  CreateChatCompletionRequest,
  LLMParseResponsesRequest,
  WholeChatCompletionResponse,
} from "@/wab/shared/copilot/prompt-utils";
import { isAdminTeamEmail } from "@/wab/shared/devflag-utils";
import { Request, Response } from "express-serve-static-core";
import { stringify } from "safe-stable-stringify";
import { Connection } from "typeorm";
import { processChatRequest, runTscAndGetOutput } from "./sandbox/copilot";

async function processCodeRequest(
  mgr: DbMgr,
  {
    currentCode,
    data,
    goal,
    useClaude,
    projectId,
    ...req
  }: QueryCopilotCodeRequest | QueryCopilotSqlCodeRequest
) {
  const llm = useClaude ? createAnthropicClient(mgr) : createOpenAIClient(mgr);

  let lastRequest = undefined as CreateChatCompletionRequest | undefined;

  const queryResult: WholeChatCompletionResponse =
    req.type === "code"
      ? await createAndRunCopilotCodeChain({
          currentCode,
          data,
          goal,
          context: req.context,
          executeRequest: (_req) => {
            lastRequest = _req;
            return llm.createChatCompletion(_req);
          },
        })
      : (
          await createAndRunCopilotSqlCodeChain({
            currentCode,
            data,
            goal,
            dataSourceSchema: req.schema,
            executeRequest: (_req) => {
              lastRequest = _req;
              return llm.createChatCompletion(_req);
            },
          })
        ).composed;

  const { id: copilotInteractionId } = await mgr.createCopilotInteraction({
    model: useClaude ? "claude" : "gpt",
    projectId,
    userPrompt: goal,
    response: ensure(
      queryResult.choices[0].message?.content,
      () => `No response content`
    ),
    request: ensure(lastRequest, () => `No request`),
  });

  const result: CopilotResponseData = Object.assign(
    { data: queryResult },
    {
      copilotInteractionId,
    }
  );

  const response = JSON.stringify(result);
  return response;
}

export async function processCopilot(
  dbCon: Connection,
  mgr: DbMgr,
  req:
    | QueryCopilotChatRequest
    | QueryCopilotCodeRequest
    | QueryCopilotSqlCodeRequest
) {
  await mgr.useCopilotAndCheckRateLimit();
  return req.type === "chat"
    ? processChatRequest(dbCon, mgr, req)
    : processCodeRequest(mgr, req);
}

export async function queryCopilot(req: Request, res: Response) {
  const mgr = userDbMgr(req);
  const payload = req.body as QueryCopilotRequest;
  if (
    payload.type !== "code" &&
    payload.type !== "code-sql" &&
    !isAdminTeamEmail(req.user?.email, req.devflags)
  ) {
    throw new BadRequestError();
  }
  const response: QueryCopilotResponse =
    payload.type === "debug"
      ? payload.rawDebug
        ? {
            response: "",
            rawDebug: stringify(
              await interact(
                JSON.parse(payload.rawDebug),
                payload.useClaude
                  ? createAnthropicClient(mgr)
                  : createOpenAIClient(mgr)
              )
            ),
          }
        : payload.typeDebug
        ? {
            response: "",
            typeDebug: await runTscAndGetOutput(payload.typeDebug),
          }
        : {
            response: "",
            dataSourcesDebug: JSON.stringify(
              (await maybe(
                await mgr
                  .getProjectById(payload.projectId)
                  .then((p) => p.workspaceId),
                (wid) => mgr.getWorkspaceDataSources(wid)
              )) ?? []
            ),
          }
      : { response: await processCopilot(req.con, mgr, payload) };
  res.json(ensureType<QueryCopilotResponse>(response));
}

async function processUiCopilotRequest(
  mgr: DbMgr,
  { images, goal, useClaude, projectId, tokens, ...req }: QueryCopilotUiRequest
) {
  const openaiClient = getOpenAI();
  const llmRequest = createCopilotUiParseRequestParams({
    goal,
    images,
    tokens,
  });
  const queryResult = await openaiClient.responses.parse<
    LLMParseResponsesRequest,
    CopilotUiActions
  >(llmRequest);

  const { id: copilotInteractionId } = await mgr.createCopilotInteraction({
    model: "gpt",
    projectId,
    userPrompt: goal,
    response: ensure(queryResult.output_text, () => `No response content`),
    request: ensure(llmRequest, () => `No request`),
  });

  const response = {
    data: queryResult.output_parsed,
    copilotInteractionId,
  };
  return response;
}

export async function queryUiCopilot(req: Request, res: Response) {
  const mgr = userDbMgr(req);
  const payload = req.body as QueryCopilotUiRequest;
  if (
    payload.type !== "ui" &&
    !isAdminTeamEmail(req.user?.email, req.devflags)
  ) {
    throw new BadRequestError();
  }
  await mgr.useCopilotAndCheckRateLimit();

  const response = await processUiCopilotRequest(mgr, payload);
  res.json(ensureType<QueryCopilotUiResponse>(response));
}

export async function saveCopilotFeedback(req: Request, res: Response) {
  const mgr = userDbMgr(req);
  const { feedback, id, feedbackDescription, projectId } =
    req.body as SendCopilotFeedbackRequest;
  await mgr.saveCopilotFeedback({
    copilotInteractionId: id,
    feedback,
    feedbackDescription,
    projectId,
  });
  res.json({});
}

export async function queryCopilotFeedback(req: Request, res: Response) {
  const pageSize: number = JSON.parse(
    ensure(req.query.pageSize as string, () => `No pageSize`)
  ) as number;
  const pageIndex: number = JSON.parse(
    ensure(req.query.pageIndex as string, () => `No pageIndex`)
  ) as number;
  const query: string | undefined = req.query.query
    ? JSON.parse(req.query.query as string)
    : undefined;
  const mgr = superDbMgr(req);
  const result = await mgr.queryCopilotFeedback({ pageSize, pageIndex, query });
  res.json(ensureType<QueryCopilotFeedbackResponse>(result));
}
