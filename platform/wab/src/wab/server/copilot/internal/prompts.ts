import { Co<PERSON><PERSON><PERSON>oken } from "@/wab/shared/ApiSchema";
import {
  CopilotUiActionsSchema,
  CopilotUiChainProps,
  LLMParseResponsesRequest,
} from "@/wab/shared/copilot/prompt-utils";
import { asDataUrl } from "@/wab/shared/data-urls";
import { zodTextFormat } from "openai/helpers/zod";
import { ResponseInputImage } from "openai/resources/responses/responses";

export function createCopilotUiParseRequestParams({
  goal,
  images,
  tokens,
}: CopilotUiChainProps): LLMParseResponsesRequest {
  const imageUrlTypeContent: ResponseInputImage[] = (images || []).map(
    (image) => ({
      type: "input_image",
      image_url: asDataUrl(image.base64, `image/${image.type}`, "base64"),
      detail: "auto",
    })
  );

  return {
    model: "gpt-4.1-mini",
    temperature: 0.7,
    text: {
      format: zodTextFormat(
        CopilotUiActionsSchema,
        "copilot_ui_structured_response"
      ),
    },
    input: [
      {
        role: "system",
        content: `You are an expert HTML designer tasked with creating a webpage based on a provided description. Your goal is to produce a list of design actions in right sequence and data.

Follow these HTML code guidelines strictly:

# General design and formatting guidelines:
- For css, use inline css in styles attribute. Do not use classes.
- For all colors, use hex values, do not use meaningful color names such as white, red, use hex value instead.
- Return only the full code in <body></body> tags. Do not include markdown "\`\`\`" or "\`\`\`html" at the start or end.
- Repeat elements as needed. If there are 15 items described, your code should include all 15 items. Do not use comments like "<!-- Repeat for each news item -->".
- Do not add comments in the code such as "<!-- Add other navigation links as needed -->" or "<!-- ... other news items ... -->" in place of writing the full code. Write the complete code for all elements.
- DON'T assume default css values elements. Always provide css based on the element styling.

# Icons and Images guidelines:
- For fonts, you can use Google Fonts. Always use only single actual font name in font-family css, don't provide fallback font name. For example, use single font name such as font-family: 'Roboto' instead of 'Roboto, sans-serif'.
- For icons, ALWAYS use img tags with placeholder images from https://placehold.co.
- For images, use placeholder images from https://placehold.co. Include a detailed description of the image in the alt text so that an image generation AI can generate the image later.
- Always provide width and height style properties for <img> tag

# Layout guidelines:
- ALWAYS use flex layout and always provide flex-direction, justify-content and align-items values in css.

${tokens ? prepareStructuredUiTokensPrompt(tokens) : ""}
`,
      },
      {
        role: "user",
        content: [
          {
            type: "input_text",
            text: `Provide your complete HTML code inside <body></body> tags. Do not include any explanation or comments outside of these tags. Your response should contain only valid HTML code that could be directly used to create the webpage.

Think step by step to generate the HTML Document Body.

Here is the user goal: ${goal}
          `.trim(),
          },
          ...imageUrlTypeContent,
        ],
      },
    ],
  };
}

function prepareStructuredUiTokensPrompt(tokens: CopilotToken[]) {
  /* The following prompt is subject to further experimentation and should be improved with more guidelines on how the tokens should be used
   * currently, it sometimes tries to use existing tokens based on semantic names even if the intended design mismatch. For example, if I upload a screenshot
   * of a design with a hero section having a larger heading text lets say 40px, but we have a token for Font-LG which could be 18px-24px, but because of semantic name
   * it will consider using it sometimes.
   *
   * Overall, the prompt should be designed in way to have freedom of usage for design tokens as well as to ensure that it doesn't bloat all the values with design tokens to avoid flat designs
   * This could also further be improved when we support add/modify tokens, in that case AI can infer new design tokens to be considered or maybe modify existing one to ensure design tokens
   * are used but also ensure the design output is correct by adjust the token value if needed.
   */
  return `# Design Tokens Guidelines
- Design tokens should always be used in the given format i.e var(--token-<uuid>) where uuid is a unique identifier provided for each token below.
  In case of new tokens where the uuid doesn't exist, use the name format i.e var(--token-<name>) where name is the newly generated token name.
- You should only use existing design tokens when your intended value matches exactly the provided design token value. Make sure you match the value not just the semantic name for a given style.
- In case you didn't find a design token for a given value while generating a design, you can consider adding a new token for that using same naming convention.

Following is a list of all design tokens that user currently have access to in their project.
${tokens
  .map(
    (token) => `name: ${token.name}
type: ${token.type}
uuid: ${token.uuid}
value: ${token.value}
`
  )
  .join("\n")}
`;
}
