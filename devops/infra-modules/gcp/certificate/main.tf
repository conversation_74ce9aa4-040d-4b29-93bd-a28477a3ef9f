resource "google_certificate_manager_dns_authorization" "instance" {
  for_each = var.dns_authorizations

  project     = var.project
  name        = each.key
  description = each.value.description
  domain      = each.value.domain
  type        = each.value.type
  location    = each.value.location
}

resource "google_certificate_manager_certificate" "instance" {
  for_each = var.certificates

  project     = var.project
  name        = each.key
  description = each.value.description
  scope       = each.value.scope
  managed {
    domains            = each.value.managed.domains
    dns_authorizations = [for d in each.value.managed.dns_authorizations : google_certificate_manager_dns_authorization.instance[d].id]
  }
}

locals {
  certificate_map_entries = flatten([
    for map_name, map_obj in var.certificate_maps : [
      for entry_name, entry in map_obj.entries : {
        map_name     = map_name
        entry_name   = entry_name
        certificates = entry.certificates
        hostname     = lookup(entry, "hostname", null)
        matcher      = lookup(entry, "matcher", null)
        description  = lookup(entry, "description", null)
      }
    ]
  ])
}

resource "google_certificate_manager_certificate_map" "instance" {
  for_each = var.certificate_maps

  project     = var.project
  name        = each.key
  description = each.value.description
}

resource "google_certificate_manager_certificate_map_entry" "instance" {
  for_each = tomap({
    for entry in local.certificate_map_entries : "${entry.entry_name}.${entry.map_name}" => entry
  })

  project      = var.project
  name         = each.value.map_name
  description  = each.value.description
  certificates = [for c in each.value.certificates : google_certificate_manager_certificate.instance[c].id]
  map          = google_certificate_manager_certificate_map.instance[each.value.map_name].name
  matcher      = each.value.matcher
  hostname     = each.value.hostname
}
