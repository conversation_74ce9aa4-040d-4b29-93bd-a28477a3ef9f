output "dns_authorizations" {
  description = "dns authorizations info"
  value = {
    for key, value in google_certificate_manager_dns_authorization.instance : key => {
      record_name  = value.dns_resource_record[0].name
      record_type  = value.dns_resource_record[0].type
      record_value = value.dns_resource_record[0].data
    }
  }
}

output "certificates" {
  description = "certificate map name => id"
  value = {
    for key, value in google_certificate_manager_certificate.instance : key => {
      id = value.id
    }
  }
}

output "certificate_maps" {
  description = "certificate map name => id"
  value = {
    for key, value in google_certificate_manager_certificate_map.instance : key => {
      id = value.id
    }
  }
}
