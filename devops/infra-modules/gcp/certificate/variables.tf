variable "project" {
  description = "The project to deploy to, if not set the default provider project is used."
  type        = string
}

variable "dns_authorizations" {
  description = "A list of dns authorizations"
  type = map(object({
    description = optional(string)
    domain      = string
    type        = optional(string)
    location    = optional(string)
  }))
  default = {}
}

variable "certificates" {
  description = "A list of certificates"
  type = map(object({
    description = optional(string)
    scope       = optional(string)
    managed = object({
      domains            = list(string)
      dns_authorizations = list(string)
    })
  }))
  default = {}
}

variable "certificate_maps" {
  description = "map of certificate maps to create"
  type = map(object({
    description = optional(string)
    entries = map(object({
      description  = optional(string)
      certificates = list(string)
      hostname     = optional(string)
      matcher      = optional(string)
    }))
  }))

}
