resource "google_network_services_edge_cache_origin" "this" {
  for_each = var.origins

  provider = google-beta
  project  = var.project

  name             = each.key
  origin_address   = each.value["origin_address"]
  description      = each.value["description"]
  max_attempts     = each.value["max_attempts"]
  protocol         = each.value["protocol"]
  port             = each.value["port"]
  retry_conditions = each.value["retry_conditions"]

  dynamic "timeout" {
    for_each = each.value["timeout"] == null ? [] : [1]

    content {
      connect_timeout      = each.value["timeout"]["connect_timeout"]
      max_attempts_timeout = each.value["timeout"]["max_attempts_timeout"]
      response_timeout     = each.value["timeout"]["response_timeout"]
      read_timeout         = each.value["timeout"]["read_timeout"]
    }
  }

  dynamic "origin_override_action" {
    for_each = each.value["origin_override_action"] == null ? [] : [1]
    content {
      dynamic "url_rewrite" {
        for_each = each.value["origin_override_action"]["url_rewrite"] == null ? [] : [1]
        content {
          host_rewrite = each.value["origin_override_action"]["url_rewrite"]["host_rewrite"]
        }
      }
      dynamic "header_action" {
        for_each = each.value["origin_override_action"]["header_action"] == null ? [] : [1]
        content {
          dynamic "request_headers_to_add" {
            for_each = each.value["origin_override_action"]["header_action"]["request_headers_to_add"] == null ? [] : each.value["origin_override_action"]["header_action"]["request_headers_to_add"]
            content {
              header_name  = request_headers_to_add.value["header_name"]
              header_value = request_headers_to_add.value["value"]
              replace      = request_headers_to_add.value["replace"]
            }
          }
        }
      }
    }
  }
  dynamic "origin_redirect" {
    for_each = each.value["origin_redirect"] == null ? [] : [1]
    content {
      redirect_conditions = each.value["origin_redirect"]["redirect_conditions"]
    }
  }
}

resource "google_network_services_edge_cache_service" "this" {
  for_each = var.services

  provider = google-beta
  project  = var.project

  name                  = each.key
  description           = each.value.description
  disable_http2         = lookup(each.value, "disable_http2", null)
  disable_quic          = lookup(each.value, "disable_quic", null)
  edge_security_policy  = lookup(each.value, "edge_security_policy", null)
  edge_ssl_certificates = lookup(each.value, "edge_ssl_certificates", null)
  labels                = lookup(each.value, "labels", null)
  require_tls           = lookup(each.value, "require_tls", null)
  ssl_policy            = lookup(each.value, "ssl_policy", null)

  dynamic "log_config" {
    for_each = each.value.log_config == null ? [] : [each.value.log_config]
    content {
      enable      = log_config.value.enable
      sample_rate = log_config.value.sample_rate
    }
  }

  dynamic "timeouts" {
    for_each = each.value.timeouts == null ? [] : [each.value.timeouts]
    content {
      create = timeouts.value.create
      update = timeouts.value.update
      delete = timeouts.value.delete
    }
  }

  routing {
    # Host rule: define which hostnames this cache service applies to
    dynamic "host_rule" {
      for_each = each.value.routing.host_rules
      content {
        description  = host_rule.value.description
        hosts        = host_rule.value.hosts
        path_matcher = host_rule.value.path_matcher
      }
    }

    dynamic "path_matcher" {
      for_each = each.value.routing.path_matchers
      content {
        name        = path_matcher.key
        description = path_matcher.value.description

        dynamic "route_rule" {
          for_each = lookup(path_matcher.value, "route_rules", [])
          content {
            priority    = route_rule.value.priority
            description = route_rule.value.description
            origin      = route_rule.value.origin

            dynamic "route_methods" {
              for_each = route_rule.value.route_methods == null ? [] : [route_rule.value.route_methods]
              content {
                allowed_methods = route_methods.value.allowed_methods
              }
            }

            # Match conditions block
            dynamic "match_rule" {
              for_each = route_rule.value.match_rule == null ? [] : route_rule.value.match_rule
              content {
                ignore_case         = match_rule.value.ignore_case
                prefix_match        = match_rule.value.prefix_match
                path_template_match = match_rule.value.path_template_match
                full_path_match     = match_rule.value.full_path_match
                dynamic "header_match" {
                  for_each = match_rule.value.header_match == null ? [] : match_rule.value.header_match
                  content {
                    header_name   = header_match.value.header_name
                    suffix_match  = header_match.value.suffix_match
                    present_match = header_match.value.present_match
                    prefix_match  = header_match.value.prefix_match
                    invert_match  = header_match.value.invert_match
                    exact_match   = header_match.value.exact_match
                  }
                }
                dynamic "query_parameter_match" {
                  for_each = match_rule.value.query_parameter_match == null ? [] : match_rule.value.query_parameter_match
                  content {
                    name          = query_parameter_match.value.name
                    present_match = query_parameter_match.value.present_match
                    exact_match   = query_parameter_match.value.exact_match
                  }
                }
              }
            }

            # Header actions (add/remove)
            dynamic "header_action" {
              for_each = route_rule.value.header_action == null ? [] : [route_rule.value.header_action]
              content {
                dynamic "request_header_to_add" {
                  for_each = header_action.value.request_headers_to_add == null ? [] : header_action.value.request_headers_to_add
                  content {
                    header_name  = request_header_to_add.value.header_name
                    header_value = request_header_to_add.value.header_value
                    replace      = request_header_to_add.value.replace
                  }
                }
                dynamic "request_header_to_remove" {
                  for_each = header_action.value.request_headers_to_remove == null ? [] : header_action.value.request_headers_to_remove
                  content {
                    header_name = request_header_to_remove.value.header_name
                  }
                }
                dynamic "response_header_to_add" {
                  for_each = header_action.value.response_headers_to_add == null ? [] : header_action.value.response_headers_to_add
                  content {
                    header_name  = response_header_to_add.value.header_name
                    header_value = response_header_to_add.value.header_value
                    replace      = response_header_to_add.value.replace
                  }
                }
                dynamic "response_header_to_remove" {
                  for_each = header_action.value.response_headers_to_remove == null ? [] : header_action.value.response_headers_to_remove
                  content {
                    header_name = response_header_to_remove.value.header_name
                  }
                }
              }
            }

            # Route action (cdn policies, url rewrite, cors)
            dynamic "route_action" {
              for_each = route_rule.value.route_action == null ? [] : [route_rule.value.route_action]
              content {
                # CDP policy block
                dynamic "cdn_policy" {
                  for_each = route_action.value.cdn_policy == null ? [] : [route_action.value.cdn_policy]
                  content {
                    cache_mode  = cdn_policy.value.cache_mode
                    client_ttl  = cdn_policy.value.client_ttl
                    default_ttl = cdn_policy.value.default_ttl
                    max_ttl     = cdn_policy.value.max_ttl
                    dynamic "cache_key_policy" {
                      for_each = cdn_policy.value.cache_key_policy == null ? [] : [cdn_policy.value.cache_key_policy]
                      content {
                        included_query_parameters = cache_key_policy.value.included_query_parameters
                        included_header_names     = cache_key_policy.value.included_header_names
                        included_cookie_names     = cache_key_policy.value.included_cookie_names
                        include_protocol          = cache_key_policy.value.include_protocol
                        excluded_query_parameters = cache_key_policy.value.excluded_query_parameters
                        exclude_query_string      = cache_key_policy.value.exclude_query_string
                        exclude_host              = cache_key_policy.value.exclude_host
                      }
                    }
                    negative_caching        = cdn_policy.value.negative_caching
                    negative_caching_policy = cdn_policy.value.negative_caching_policy
                    # ... other cdn_policy fields
                  }
                }

                # URL rewrite
                dynamic "url_rewrite" {
                  for_each = route_action.value.url_rewrite == null ? [] : [route_action.value.url_rewrite]
                  content {
                    path_prefix_rewrite   = url_rewrite.value.path_prefix_rewrite
                    host_rewrite          = url_rewrite.value.host_rewrite
                    path_template_rewrite = url_rewrite.value.path_template_rewrite
                  }
                }

                dynamic "cors_policy" {
                  for_each = route_action.value.cors_policy == null ? [] : [route_action.value.cors_policy]
                  content {
                    max_age           = cors_policy.value.max_age
                    allow_credentials = cors_policy.value.allow_credentials
                    expose_headers    = cors_policy.value.expose_headers
                    disabled          = cors_policy.value.disabled
                    allow_origins     = cors_policy.value.allow_origins
                    allow_methods     = cors_policy.value.allow_methods
                    allow_headers     = cors_policy.value.allow_headers
                  }
                }
              }
            }

            # URL redirect
            dynamic "url_redirect" {
              for_each = route_rule.value.url_redirect == null ? [] : [route_rule.value.url_redirect]
              content {
                host_redirect   = url_redirect.value.host_redirect
                https_redirect  = url_redirect.value.https_redirect
                path_redirect   = url_redirect.value.path_redirect
                prefix_redirect = url_redirect.value.prefix_redirect
              }
            }
          }
        }
      }
    }
  }
}
