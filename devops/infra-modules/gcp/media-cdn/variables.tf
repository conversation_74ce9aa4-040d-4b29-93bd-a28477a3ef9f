variable "project" {
  description = "The project to deploy to, if not set the default provider project is used."
  type        = string
}

variable "origins" {
  description = "The project to deploy to, if not set the default provider project is used."
  type = map(object({
    origin_address   = string
    description      = optional(string)
    max_attempts     = optional(number)
    protocol         = optional(string)
    port             = optional(number)
    retry_conditions = optional(list(string))
    timeout = optional(object({
      connect_timeout      = optional(string)
      max_attempts_timeout = optional(string)
      response_timeout     = optional(string)
      read_timeout         = optional(string)
    }))
    origin_override_action = optional(object({
      url_rewrite = optional(object({
        host_rewrite = string
      }))
      header_action = optional(object({
        request_headers_to_add = optional(list(object({
          header_name  = string
          header_value = string
          replace      = optional(bool)
        })))
        request_headers_to_remove = optional(list(object({
          header_name = string
        })))
        response_headers_to_add = optional(list(object({
          header_name  = string
          header_value = string
          replace      = optional(bool)
        })))
        response_headers_to_remove = optional(list(object({
          header_name = string
        })))
      }))
    }))
    origin_redirect = optional(object({
      redirect_conditions = list(string)
    }))
  }))
}

variable "services" {
  description = "Map of Edge Cache Service configurations"
  type = map(object({
    description           = optional(string)
    disable_http2         = optional(bool)
    disable_quic          = optional(bool)
    edge_security_policy  = optional(string)
    edge_ssl_certificates = optional(list(string))
    labels                = optional(map(string))
    log_config = optional(object({
      enable      = optional(bool)
      sample_rate = optional(number)
    }))
    require_tls = optional(bool)
    ssl_policy  = optional(string)
    timeouts = optional(object({
      create = optional(string)
      update = optional(string)
      delete = optional(string)
    }))
    routing = object({
      host_rules = list(object({
        description  = optional(string)
        hosts        = list(string)
        path_matcher = string
      }))
      path_matchers = map(object({
        description = optional(string)
        route_rules = optional(list(object({
          priority    = number
          description = optional(string)
          origin      = optional(string)
          route_methods = optional(object({
            allowed_methods = list(string)
          }))
          match_rule = optional(list(object({
            ignore_case         = optional(bool)
            prefix_match        = optional(string)
            path_template_match = optional(string)
            full_path_match     = optional(string)
            header_match = optional(list(object({
              header_name   = string
              present_match = optional(bool)
              exact_match   = optional(string)
              prefix_match  = optional(string)
              suffix_match  = optional(string)
              invert_match  = optional(bool)
            })))
            query_parameter_match = optional(list(object({
              name          = string
              present_match = optional(bool)
              exact_match   = optional(string)
            })))
          })))
          header_action = optional(object({
            request_headers_to_add = optional(list(object({
              header_name  = string
              header_value = string
              replace      = optional(bool)
            })))
            request_headers_to_remove = optional(list(object({
              header_name = string
            })))
            response_headers_to_add = optional(list(object({
              header_name  = string
              header_value = string
              replace      = optional(bool)
            })))
            response_headers_to_remove = optional(list(object({
              header_name = string
            })))
          }))
          route_action = optional(object({
            cdn_policy = optional(object({
              cache_mode              = optional(string)
              client_ttl              = optional(string)
              default_ttl             = optional(string)
              max_ttl                 = optional(string)
              negative_caching        = optional(bool)
              negative_caching_policy = optional(map(string))
              cache_key_policy = optional(object({
                included_query_parameters = optional(list(string))
                included_header_names     = optional(list(string))
                included_cookie_names     = optional(list(string))
                include_protocol          = optional(bool)
                excluded_query_parameters = optional(list(string))
                exclude_query_string      = optional(bool)
                exclude_host              = optional(bool)
              }))
            }))
            url_rewrite = optional(object({
              path_prefix_rewrite   = optional(string)
              host_rewrite          = optional(string)
              path_template_rewrite = optional(string)
            }))
            cors_policy = optional(object({
              max_age           = string
              allow_credentials = optional(bool)
              expose_headers    = optional(list(string))
              disabled          = optional(bool)
              allow_origins     = optional(list(string))
              allow_methods     = optional(list(string))
              allow_headers     = optional(list(string))
            }))
          }))
          url_redirect = optional(object({
            host_redirect   = optional(string)
            https_redirect  = optional(bool)
            path_redirect   = optional(string)
            prefix_redirect = optional(string)
          }))
        })))
      }))
    })
  }))
}
