locals {
  org        = read_terragrunt_config(find_in_parent_folders("globals.hcl"))
  project    = read_terragrunt_config(find_in_parent_folders("project.hcl"))
  project_id = local.project.locals.project_id
}

terraform {
  source = "**************:plasmicapp/plasmic-internal.git//devops/infra-modules/gcp/media-cdn?ref=master"
}

include {
  path = find_in_parent_folders()
}

dependency "studio-spa" {
  config_path = "../../us/gcs/studio"
}

dependency "certificate" {
  config_path = "../../global/cert-manager"
}

inputs = {
  project = local.project_id
  origins = {
    site-assets = {
      origin_address = "gs://plasmic-site-assets-dev"
      timeout = {
        connect_timeout = "1s"
      }
    }
    studio-spa = {
      origin_address = dependency.studio-spa.outputs.url
      timeout = {
        connect_timeout = "1s"
      }
    }
    img-optimizer = {
      origin_address = "img-origin.dev.plasmic.app"
      protocol       = "HTTPS"
      timeout = {
        connect_timeout  = "10s"
        response_timeout = "120s"
      }
    }
  }
  services = {
    platform = {
      description = "Service that contains all route rules for the platform"
      edge_ssl_certificates = [
        dependency.certificate.outputs.certificates["platform-wildcard-edge"].id
      ]
      require_tls = true
      log_config = {
        enable      = true
        sample_rate = 1
      }
      routing = {
        host_rules = [
          {
            hosts        = ["site-assets.dev.plasmic.app"]
            path_matcher = "site-assets"
          },
          {
            hosts        = ["studio.dev.plasmic.app"]
            path_matcher = "studio"
          },
          {
            hosts        = ["img.dev.plasmic.app"]
            path_matcher = "img-optimizer"
          }
        ]
        path_matchers = {
          img-optimizer = {
            description = "img optimizer"
            route_rules = [
              {
                priority    = 10
                description = "cached img optimizer routes"
                origin      = "img-optimizer"
                match_rule = [
                  {
                    prefix_match = "/img-optimizer/v1/img"
                  }
                ]
                route_action = {
                  cdn_policy = {
                    cache_mode  = "CACHE_ALL_STATIC"
                    default_ttl = "86400s"
                    max_ttl     = "31536000s"
                  }
                }
                header_action = {
                  response_headers_to_add = [
                    {
                      header_name  = "Access-Control-Allow-Credentials"
                      header_value = "false"
                    },
                    {
                      header_name  = "Access-Control-Allow-Headers"
                      header_value = "*"
                    },
                    {
                      header_name  = "Access-Control-Allow-Methods"
                      header_value = "GET"
                    },
                    {
                      header_name  = "Access-Control-Allow-Origin"
                      header_value = "*"
                      replace      = true
                    },
                    {
                      header_name  = "Content-Security-Policy"
                      header_value = "script-src 'none'; Origin override"
                    },
                    {
                      header_name  = "x-robot-tag"
                      header_value = "noindex"
                    }
                  ]
                }
              },
              {
                priority    = 20
                description = "uncached optimizer routes"
                origin      = "img-optimizer"
                route_methods = {
                  allowed_methods = ["GET", "HEAD", "OPTIONS", "PUT", "POST", "DELETE", "PATCH"]
                }
                match_rule = [
                  {
                    prefix_match = "/"
                  }
                ]
                route_action = {
                  cdn_policy = {
                    cache_mode = "BYPASS_CACHE"
                  }
                }
              }
            ]
          }
          studio = {
            description = "studio routing"
            route_rules = [
              {
                priority    = 10
                description = "studio spa"
                origin      = "studio-spa"
                match_rule = [
                  {
                    prefix_match = "/"
                  }
                ]
                route_action = {
                  cdn_policy = {
                    cache_mode = "CACHE_ALL_STATIC"
                  }
                }
              }
            ]
          }
          site-assets = {
            description = "site assets path matcher"
            route_rules = [
              {
                priority    = 10
                description = "default"
                origin      = "site-assets"
                match_rule = [
                  {
                    prefix_match = "/"
                  }
                ]
                route_action = {
                  cdn_policy = {
                    cache_mode  = "CACHE_ALL_STATIC"
                    default_ttl = "86400s"
                    max_ttl     = "31536000s"
                  }
                }
                header_action = {
                  response_headers_to_add = [
                    {
                      header_name  = "Access-Control-Allow-Credentials"
                      header_value = "false"
                    },
                    {
                      header_name  = "Access-Control-Allow-Headers"
                      header_value = "*"
                    },
                    {
                      header_name  = "Access-Control-Allow-Methods"
                      header_value = "GET"
                    },
                    {
                      header_name  = "Access-Control-Allow-Origin"
                      header_value = "*"
                    },
                    {
                      header_name  = "x-robot-tag"
                      header_value = "noindex"
                    }
                  ]
                }
              }
            ]
          }
        }
      }
    }
  }
}
