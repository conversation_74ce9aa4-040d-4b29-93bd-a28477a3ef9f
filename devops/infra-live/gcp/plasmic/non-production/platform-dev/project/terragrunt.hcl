locals {
  org_variables        = read_terragrunt_config(find_in_parent_folders("globals.hcl"))
  env_variables        = read_terragrunt_config(find_in_parent_folders("env.hcl"))
  project_variables    = read_terragrunt_config(find_in_parent_folders("project.hcl"))
  project_id           = local.project_variables.locals.project_id
  folder_id            = local.env_variables.locals.folder_id
  org_id               = local.org_variables.locals.org_id
  billing_account      = local.org_variables.locals.billing_account
  svpc_host_project_id = local.org_variables.locals.svpc_host_project_id
}

terraform {
  source = "tfr:///terraform-google-modules/project-factory/google//?version=17.1.0"
}

include {
  path = find_in_parent_folders()
}

inputs = {
  name = "platform-dev"
  activate_apis = [
    "compute.googleapis.com",
    "container.googleapis.com",
    "cloudkms.googleapis.com",
    "cloudresourcemanager.googleapis.com",
    "apigateway.googleapis.com",
    "servicecontrol.googleapis.com",
    "servicemanagement.googleapis.com",
    "storagetransfer.googleapis.com",
    "networkservices.googleapis.com",
  ]
  project_id               = local.project_id
  folder_id                = local.folder_id
  random_project_id        = false
  random_project_id_length = 0
  default_service_account  = "disable"
  org_id                   = local.org_id
  billing_account          = local.billing_account
  svpc_host_project_id     = local.svpc_host_project_id
}
