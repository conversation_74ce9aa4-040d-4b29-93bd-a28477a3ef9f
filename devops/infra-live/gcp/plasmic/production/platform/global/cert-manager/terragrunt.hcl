locals {
  org        = read_terragrunt_config(find_in_parent_folders("globals.hcl"))
  project    = read_terragrunt_config(find_in_parent_folders("project.hcl"))
  project_id = local.project.locals.project_id
}

terraform {
  source = "**************:plasmicapp/plasmic-internal.git//devops/infra-modules/gcp/certificate?ref=master"
}

include {
  path = find_in_parent_folders()
}

inputs = {
  project = local.project_id
  dns_authorizations = {
    wildcard = {
      domain = "plasmic.app"
    }
    wildcard-plasmicdev = {
      domain = "plasmicdev.com"
    }
  }
  certificates = {
    platform-wildcard-edge = {
      scope = "EDGE_CACHE"
      managed = {
        domains            = ["*.plasmic.app", "*.plasmicdev.com"]
        dns_authorizations = ["wildcard", "wildcard-plasmicdev"]
      }
    }
    platform-wildcard = {
      managed = {
        domains            = ["*.plasmic.app", "*.plasmicdev.com"]
        dns_authorizations = ["wildcard", "wildcard-plasmicdev"]
      }
    }
  }
  certificate_maps = {
    platform-lb = {
      description = "certificate map to be attached to the platform loadbalancer"
      entries = {
        wildcard = {
          description  = "default wildcard certificate"
          certificates = ["platform-wildcard"]
          matcher      = "PRIMARY"
        }
      }
    }
  }
}
