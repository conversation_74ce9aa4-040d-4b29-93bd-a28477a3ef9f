#!/usr/bin/env bash

# Tips:
# tar -C ~ -cz _diag | curl --upload-file - https://transfer.sh/diag.tgz

set -ex
set -o pipefail

# Playwright parallelism doesn't do that much since things are so staggered with the yarn mutexes
export CYPRESS_PARALLELISM=12 PLAYWRIGHT_PARALLELISM=12 CI_FAILFAST=1

self="$(readlink -f "$0")"

# It's possible we are "operating on" a different git checkout than the one this script is running from. For instance, to test CD changes, we might be hacking on cicd and devops scripts, but operating on a separate master branch (for lerna, etc.).
# Hence, selfdir is the directory this script and devops scripts are run from,
# while basedir is where the code we're "operating on" is located.
#
# Note that this is a bit precarious, since "devops scripts" can also include .delivery scripts and whatnot that are inside the app directories.
# But this does let us test out tentative CD changes on the real master.
selfdir="$(readlink -f "$(dirname "$0")/..")"
basedir="${BASEDIR:-$selfdir}"

platformdir="$basedir/platform"

if type gtimeout &>/dev/null; then
  timeoutcli=gtimeout
else
  timeoutcli=timeout
fi

# Variables instead of functions, so we can pass them as arguments without needing to recurse into another bash process
concurrently="npx --yes concurrently@8.2.1 --timings"
if [[ $CI_FAILFAST ]]; then
  concurrently_args='--kill-others-on-fail' MAX_FAILURES='--max-failures 1'
fi
concurrently_ff="$concurrently $concurrently_args"

allow_fail() {
  "$@" && LAST_STATUS=$? || LAST_STATUS=$?
}

# This is useful for yarn install, which fails sometimes with ENOENT when run
# concurrently.
function safeyarn() {
  # yarn --mutex file:/tmp/.yarn-mutex "$@"
  # flock: For some reason, yarn --mutex contention can cause yarn install to just quietly abort!
  flock /tmp/yarn-mutex yarn --mutex network "$@"
}

function info() {
  echo "-> $@"
}

function error() {
  echo "Error: $@" >&2
}

function setup_venv() {
  # setup venv
  if [ ! -f venv.done ]; then
    rm -rf venv
    # virtualenv on jenkins
    python -m virtualenv venv || python -m venv venv
    touch venv.done
  fi
  source venv/bin/activate

  # Install pre-commit
  pip install pre-commit

  # Use /home/<USER>/bin for docker
  export PATH=/home/<USER>/bin:$PATH

  # Use asdf - only on jenkins not k8s
  . $HOME/.asdf/asdf.sh || true

  #  asdf install || true

  npm i -g yarn @sentry/cli || true
}

function setup_canvas_pkgs() {
  pushd canvas-packages
  for d in internal_pkgs/*; do
    if [ -d "$d" ]; then
      pushd $d
      safeyarn install
      popd
    fi
  done
  safeyarn install
  yarn build
  popd
}

function setup_tag() {
  TAG=${COMMIT_HASH:0:6}
}

function setup_ci_env() {
  export CI=true
  export COMMIT_HASH="$(git rev-parse HEAD)"
  setup_tag
  free -m || true
  free || true
  uname -a || true
  lsb_release -a || true
  uname -a || true
  whoami || true
  who am i || true
  who || true
  w || true
}

# Delete all but the 5 most recent successful builds.
function cleanup_builds() {
  ls -dtp /tmp/plasmic-* | tail -n +6 | tr '\n' '\0' | xargs -0 rm -rf --
}

# Disable for now, see if it becomes a common problem. Better to trigger another deploy instead anyway to not exceed timeout.
retry-on-exc() {
  local expected_exception="$1"
  shift

  "$@"
  return

  LAST_EXCEPTION=
  for trial in {1..3}; do
    allow_fail "$@"
    if [[ "$LAST_EXCEPTION" != "$expected_exception" ]]; then
      return $LAST_STATUS
    fi
  done
  return $LAST_STATUS
}

function maybe-pretend-push-or-throw-if-outdated() {
  git show -p
  if [[ $PRETEND_DEPLOY != true ]]; then
    LAST_OUTPUT=$(mktemp)
    LAST_STATUS=0
    # Can't rely on composing functions here due to there being hidden subprocesses
    { "$@" || LAST_STATUS=$?; } 2>&1 | tee $LAST_OUTPUT
    if [[ $LAST_STATUS != 0 ]] && grep -q 'fetch first' $LAST_OUTPUT; then
      LAST_EXCEPTION=FETCH_FIRST
    fi
    return $LAST_STATUS
  fi
}

function release_public_packages_and_publish_hostless() {
  # Make sure we are in the right directory first.
  if [[ "$BASEDIR" ]]; then
    cd "$BASEDIR"
  fi
  retry-on-exc FETCH_FIRST _release_public_packages_and_publish_hostless "$@"
}

function _release_public_packages_and_publish_hostless() {
  setup_ci_env
  sudo rm -f /continue
  if [[ $SKIP_PACKAGES != true ]]; then
    if [[ $PRETEND_DEPLOY == true ]]; then
      init_verdaccio
      start_verdaccio local
    fi
    release_public_packages
    if [[ $PRETEND_DEPLOY == true ]]; then
      start_verdaccio proxy
    fi
    # Don't run `yarn upgrade-internal` since it resets NPM_CONFIG_REGISTRY
    NPM_CONFIG_REGISTRY=$NPM_REGISTRY bash scripts/upgrade-internal.bash
    GIT_COMMITTER_NAME="Jenkins" GIT_COMMITTER_EMAIL="<EMAIL>" allow_fail git commit --author="Jenkins <<EMAIL>>" -m "upgrade-internal" -a
    if [[ $LAST_STATUS == 0 ]]; then
      # If we have stuff to push, then push and deploy
      maybe-pretend-push-or-throw-if-outdated git push
    fi
  fi

  pushd $platformdir
  if [[ $SKIP_UPDATE_HOSTLESS != true && $PRETEND_DEPLOY != true ]]; then
    # This creates migrations that need to be deployed, but we don't deploy here.
    # Instead, since this is committing/pushing to the repo, that should result in another deploy run - we'll let that one do the work!
    publish_hostless
  fi
  popd
}

function fresh_yarn_install() {
  safeyarn install
}

function run_db_script() {
  setup_venv
  pushd $platformdir/wab
  yarn
  make
  SITE_ASSETS_BUCKET='plasmic-site-assets' SITE_ASSETS_BASE_URL='https://site-assets.plasmic.app/' "$@"
  local exit_code=$?
  popd
  if ! [[ $NO_EXIT_ON_SUCCESS ]]; then
    return $exit_code
  fi
}

PROD_DBURI=postgresql://<EMAIL>/wab

function db_prune() {
  local aggressive_args=
  if [[ "${AGGRESSIVE}" == "true" ]]; then
    aggressive_args='--aggressive'
  fi
  local limit_args=
  if [[ $LIMIT_NUM ]]; then
    limit_args="--limit $LIMIT_NUM"
  fi
  run_db_script yarn db:prune --dburi=$PROD_DBURI $aggressive_args $limit_args
}

function db_invariants() {
  run_db_script yarn db:invariants --dburi=$PROD_DBURI
}

function db_migrate_bundles() {
  run_db_script yarn db:migrate-bundles --dburi=$PROD_DBURI
}

function db_one_time_job() {
  run_db_script yarn db:one-time-job --dburi=$PROD_DBURI --script=$SCRIPT --projectId=$PROJECT_ID
}

function get_free_port() {
  while true; do
    local port=$((RANDOM % 60000 + 2000))
    # lsof returns 0 if taken, 1 if free
    if ! lsof -i tcp:$port >/dev/null; then
      break
    fi
  done
  echo $port
}

function log_exit() {
  echo "Start running on_exit tasks!"
}

function on_exit() {
  if [[ $exit_cmds ]]; then
    exit_cmds="$@
$exit_cmds"
  else
    exit_cmds="$@"
  fi
  local all_exit_cmds="log_exit
$exit_cmds"
  trap "$all_exit_cmds" EXIT SIGINT SIGTERM
}

function wait_for_host() {
  local app_up= url="$1" name="$2"
  for i in $(seq 1 60); do
    sleep 1
    if curl -s "$url" >/dev/null; then
      app_up=1
      break
    fi
  done

  if [[ "$app_up" != "1" ]]; then
    error "$name failed to start at $url"
    false
  fi

  info "$name accepting connections at $url"
}

function release_public_packages() {
  export CI=true
  setup_ci_env
  setup_venv

  fresh_yarn_install

  # Bootstrap / cross-link dependencies, and build everything
  # yarn lerna bootstrap

  # Undo any modified files from yarn install
  git reset --hard

  # Checkout master so `lerna version`` can work
  git checkout master

  git fetch origin master

  # Undo the git config above
  git reset --hard

  git rebase origin/master

  # Bump version
  git config --local user.email "<EMAIL>"
  git config --local user.name "Jenkins"

  local force_bump_flags=
  if [[ $FORCE_BUMP == true ]]; then
    force_bump_flags='--force-publish'
  fi
  yarn lerna version patch --exact --include-merged-tags --yes --no-push $force_bump_flags
  maybe-pretend-push-or-throw-if-outdated git push --follow-tags origin

  # plasmicapp/host depends on this, so we run this first
  yarn lerna run build --scope @plasmicapp/query

  # This is depended upon via devDependencies, which yarn lerna run does not
  # observe :-/
  yarn lerna run build --scope @plasmicapp/host

  # Limit concurrency, as too many simultaneous builds results in OOM
  NODE_OPTIONS='--max-old-space-size=8192' yarn lerna run build --concurrency 2

  # Release packages; use --graph-type all to also follow dev dependencies
  export PREPARE_NO_BUILD=true
  yarn lerna publish from-package --concurrency=8 --graph-type all --yes --no-verify-access $NPM_REGISTRY_FLAGS
  PREPARE_NO_BUILD=
}

init_verdaccio() {
  rm -rf ./verdaccio-storage ./verdaccio-config.yaml
  export VERDACCIO_PORT=$(get_free_port)

  # Set token to allow anonymous npm publishing.
  # See https://github.com/verdaccio/verdaccio/issues/212#issuecomment-308578500
  echo "//localhost:${VERDACCIO_PORT}/:_authToken=\"token\"" >>$HOME/.npmrc
}

list-descendants() {
  local children=$(ps -o pid= --ppid "$1")

  for pid in $children; do
    list-descendants "$pid"
  done

  echo "$children"
}

# We have nested levels of `npx concurrently`. Simply killing `npx` process doesn't kill the `concurrently` child process!
#
# And because `concurrently` waits for all descendants to exit, we need to make sure we're cleaning up backgrounded descendants properly.
kill-descendants() {
  local descendants=$(list-descendants "$1")
  if [[ "$descendants" ]]; then
    kill -- $descendants
  fi
}

# In CI, we start verdaccio twice - one where it's not proxying our packages to npm, so that we can publish our own local versions, and one where it is proxying so that it can forward missing package version requests to npm.
start_verdaccio() {
  local proxy="$1"

  if [[ $verdaccio_pid ]]; then
    # This is just a loose 'wait', we should ideally be waiting for the port to actually be freed, but npx takes long to start so should be fine
    kill-descendants $verdaccio_pid && wait $verdaccio_pid || true
  fi

  local proxy_line=
  if [[ $proxy == proxy ]]; then
    proxy_line='proxy: npmjs'
  fi

  # @plasmicapp/isomorphic-unfetch is published separately from public-packages
  # See https://github.com/plasmicapp/unfetch
  cat >./verdaccio-config.yaml <<EOF
storage: ./verdaccio-storage
max_body_size: 100mb
uplinks:
  npmjs:
    url: https://registry.npmjs.org/

logs: { type: stdout, format: pretty, level: error }

packages:
  '@plasmicapp/isomorphic-unfetch':
    access: \$all
    publish: \$authenticated
    unpublish: \$authenticated
    proxy: npmjs
  '@plasmicapp/react-ssr-prepass':
    access: \$all
    publish: \$authenticated
    unpublish: \$authenticated
    proxy: npmjs
  '@plasmicapp/*':
    access: \$all
    publish: \$all
    unpublish: \$all
    $proxy_line
  '@plasmicpkgs/data-table':
    access: \$all
    publish: \$authenticated
    unpublish: \$authenticated
    proxy: npmjs
  '@plasmicpkgs/luxon-parser':
    access: \$all
    publish: \$authenticated
    unpublish: \$authenticated
    proxy: npmjs
  '@plasmicpkgs/plasmic-shopify':
    access: \$all
    publish: \$authenticated
    unpublish: \$authenticated
    proxy: npmjs
  '@plasmicpkgs/*':
    access: \$all
    publish: \$all
    unpublish: \$all
    $proxy_line
  'create-plasmic-app':
    access: \$all
    publish: \$all
    unpublish: \$all
    $proxy_line
  '**':
    access: \$all
    publish: \$authenticated
    unpublish: \$authenticated
    proxy: npmjs
EOF
  NPM_CONFIG_REGISTRY= $concurrently_ff --names "verdaccio-$proxy" "npx --yes verdaccio@5.26.3 --listen 'http://0.0.0.0:$VERDACCIO_PORT' --config ./verdaccio-config.yaml" &
  export verdaccio_pid=$!
  on_exit "kill-descendants $verdaccio_pid"
  wait_for_host "http://localhost:${VERDACCIO_PORT}" "Verdaccio"

  export NPM_CONFIG_REGISTRY="http://localhost:${VERDACCIO_PORT}"
  export NPM_REGISTRY_FLAGS=--registry=$NPM_CONFIG_REGISTRY
}

function publish_hostless() {
  # Build canvas-packages before running publish-hostless
  setup_venv

  if [[ ! $K8S ]]; then
    git fetch origin master
    git reset --hard
    git rebase origin/master
  fi

  pushd "$platformdir"

  setup_canvas_pkgs

  # Make sure to clear the migrations list if we exit early
  on_exit "git add --all && git reset --hard"
  NO_EXIT_ON_SUCCESS=true run_db_script yarn db:publish-hostless --dburi=$PROD_DBURI
  popd
}

notify-slack() {
  escaped_text=$(echo "$1" | sed 's/\\/\\\\/g' | sed 's/"/\\"/g')
  curl -d "{\"text\": \"$escaped_text\"}" "$SLACK_WEBHOOK"
}

"$@"
